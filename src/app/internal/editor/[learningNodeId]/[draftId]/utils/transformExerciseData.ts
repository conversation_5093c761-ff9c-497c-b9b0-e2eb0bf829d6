import { ExerciseType } from '@/types/internal/editorial';
import { 
  ClozeExerciseSchema,
  MCSimpleExerciseSchema,
  MCMultiExerciseSchema,
  InputExerciseSchema,
  DropdownExerciseSchema,
  HighlightExerciseSchema,
  MatchingPairsExerciseSchema,
  CategorizeExerciseSchema,
  ErrorCorrectionExerciseSchema,
  TrueFalseExerciseSchema
} from '@/schemas/content/exerciseSchema';

/**
 * Transform exercise data from backend format (snake_case) to frontend format (camelCase)
 * using the appropriate schema for the exercise type.
 */
export function transformExerciseData(
  exerciseType: ExerciseType,
  rawData: Record<string, any>
): Record<string, any> {
  try {
    switch (exerciseType) {
      case 'mc-simple':
        return MCSimpleExerciseSchema.toFrontend(rawData);
      
      case 'mc-multi':
        return MCMultiExerciseSchema.toFrontend(rawData);
      
      case 'input':
        return InputExerciseSchema.toFrontend(rawData);
      
      case 'cloze':
        return ClozeExerciseSchema.toFrontend(rawData);
      
      case 'dropdown':
        return DropdownExerciseSchema.toFrontend(rawData);
      
      case 'highlight':
        return HighlightExerciseSchema.toFrontend(rawData);
      
      case 'matching-pairs':
        return MatchingPairsExerciseSchema.toFrontend(rawData);
      
      case 'categorize':
        return CategorizeExerciseSchema.toFrontend(rawData);
      
      case 'error-correction':
        return ErrorCorrectionExerciseSchema.toFrontend(rawData);
      
      case 'true-false':
        return TrueFalseExerciseSchema.toFrontend(rawData);
      
      default:
        console.warn(`Unknown exercise type: ${exerciseType}, returning raw data`);
        return rawData;
    }
  } catch (error) {
    console.error(`Failed to transform exercise data for type ${exerciseType}:`, error);
    // Return raw data as fallback
    return rawData;
  }
}
