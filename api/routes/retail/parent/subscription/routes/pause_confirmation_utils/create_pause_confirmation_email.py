def create_pause_confirmation_email(language: str, resume_date: str, paused_count: int):
    """
    Create pause confirmation email content based on language.
    
    Args:
        language: User's language preference ('de', 'en', 'fr', 'lu')
        resume_date: Formatted resume date (e.g., "September 15, 2025")
        paused_count: Number of subscriptions paused
        
    Returns:
        Tuple of (html_body, text_body, subject)
    """
    
    email_content = {
        'de': {
            'subject': 'Ihr Abonnement wurde pausiert',
            'html_title': 'Abonnement pausiert',
            'html_body': f'''
            <p><PERSON><PERSON>,</p>
            <p>Wir bestätigen, dass {'Ihr Abonnement' if paused_count == 1 else f'Ihre {paused_count} Abonnements'} erfolgreich pausiert {'wurde' if paused_count == 1 else 'wurden'}.</p>
            <p><strong>Automatische Wiederaufnahme:</strong> {resume_date}</p>
            
            <p>Viel<PERSON> Grüße,<br>Das LuxEdu Team</p>
            ''',
            'text_body': f'''
<PERSON><PERSON>,

Wir bestätigen, dass {'Ihr Abonnement' if paused_count == 1 else f'Ihre {paused_count} Abonnements'} erfolgreich pausiert {'wurde' if paused_count == 1 else 'wurden'}.

Automatische Wiederaufnahme: {resume_date}

Während der Pause:
- Es werden keine Zahlungen verarbeitet  
- {'Ihr Abonnement wird' if paused_count == 1 else 'Ihre Abonnements werden'} automatisch am angegebenen Datum wieder aktiviert


Viele Grüße,
Das LuxEdu Team
            '''
        },
        'en': {
            'subject': 'Your subscription has been paused',
            'html_title': 'Subscription Paused',
            'html_body': f'''
            <p>Hello,</p>
            <p>We confirm that your {'subscription has' if paused_count == 1 else f'{paused_count} subscriptions have'} been successfully paused.</p>
            <p><strong>Automatic resumption:</strong> {resume_date}</p>
           
            <p>Best regards,<br>The LuxEdu Team</p>
            ''',
            'text_body': f'''
Hello,

We confirm that your {'subscription has' if paused_count == 1 else f'{paused_count} subscriptions have'} been successfully paused.

Automatic resumption: {resume_date}

During the pause:
- No payments will be processed
- Your {'subscription will' if paused_count == 1 else 'subscriptions will'} automatically resume on the specified date


Best regards,
The LuxEdu Team
            '''
        },
        'fr': {
            'subject': 'Votre abonnement a été suspendu',
            'html_title': 'Abonnement suspendu',
            'html_body': f'''
            <p>Bonjour,</p>
            <p>Nous confirmons que votre {'abonnement a' if paused_count == 1 else f'{paused_count} abonnements ont'} été suspendu{'s' if paused_count > 1 else ''} avec succès.</p>
            <p><strong>Reprise automatique :</strong> {resume_date}</p>
            
            <p>Cordialement,<br>L'équipe LuxEdu</p>
            ''',
            'text_body': f'''
Bonjour,

Nous confirmons que votre {'abonnement a' if paused_count == 1 else f'{paused_count} abonnements ont'} été suspendu{'s' if paused_count > 1 else ''} avec succès.

Reprise automatique : {resume_date}

Pendant la suspension :
- Aucun paiement ne sera traité
- Votre {'abonnement reprendra' if paused_count == 1 else 'abonnements reprendront'} automatiquement à la date spécifiée


Cordialement,
L'équipe LuxEdu
            '''
        },
        'lu': {
            'subject': 'Är Abonnement gouf pauséiert',
            'html_title': 'Abonnement pauséiert',
            'html_body': f'''
            <p>Hallo,</p>
            <p>Mir bestätegen, datt Är {'Abonnement' if paused_count == 1 else f'{paused_count} Abonnementer'} erfollegräich pauséiert {'gouf' if paused_count == 1 else 'goufen'}.</p>
            <p><strong>Start erëm:</strong> {resume_date}</p>
            
            <p>Mat beschte Grëss,<br>D'LuxEdu Team</p>
            ''',
            'text_body': f'''
Hallo,

Mir bestätegen, datt Är {'Abonnement' if paused_count == 1 else f'{paused_count} Abonnementer'} erfollegräich pauséiert {'gouf' if paused_count == 1 else 'goufen'}.

Start erëm: {resume_date}

Wärend der Paus:
- Et ginn keng Bezuelungen verbuecht
- Är {'Abonnement gëtt' if paused_count == 1 else 'Abonnementer ginn'} automatesch op dem spezifizéierten Datum erëm aktivéiert


Mat beschte Grëss,
D'LuxEdu Team
            '''
        }
    }
    
    # Default to English if language not found
    content = email_content.get(language, email_content['en'])
    
    # Create full HTML email
    html_body = f'''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>{content['subject']}</title>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
            .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
            .header {{ background-color: #f8f9fa; padding: 20px; text-align: center; }}
            .content {{ padding: 20px; background-color: #ffffff; }}
            .footer {{ background-color: #f8f9fa; padding: 10px; text-align: center; font-size: 12px; color: #666; }}
            ul {{ padding-left: 20px; }}
            li {{ margin-bottom: 8px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>{content['html_title']}</h1>
            </div>
            <div class="content">
                {content['html_body']}
            </div>
            <div class="footer">
                <p>LuxEdu</p>
            </div>
        </div>
    </body>
    </html>
    '''
    
    return html_body, content['text_body'], content['subject']