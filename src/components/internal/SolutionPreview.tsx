'use client';

import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Eye, 
  CheckCircle, 
  XCircle, 
  Lightbulb, 
  Video,
  Play,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import { ExerciseType } from '@/types/internal/editorial';
import { BaseSolutionData } from '@/components/internal/SolutionEditor';
import { LatexRenderer } from '@/components/shared/LatexRenderer';

interface SolutionPreviewProps {
  exerciseType: ExerciseType;
  exerciseData: Record<string, any>;
  solutionData: BaseSolutionData;
  className?: string;
}

export default function SolutionPreview({
  exerciseType,
  exerciseData,
  solutionData,
  className
}: SolutionPreviewProps) {
  const [showSteps, setShowSteps] = useState(false);
  const [showVideo, setShowVideo] = useState(false);

  const hasSteps = !!(solutionData.solutionSteps && solutionData.solutionSteps.length > 0);
  const hasVideo = !!solutionData.videoPublicId;

  const renderCorrectAnswer = () => {
    if (!solutionData.correctAnswer) {
      return (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>No correct answer configured</AlertDescription>
        </Alert>
      );
    }

    const correctAnswer = solutionData.correctAnswer;

    switch (exerciseType) {
      case 'mc-simple':
      case 'mc-multi': {
        const options = exerciseData.options || [];
        const correctOptionIds = exerciseType === 'mc-simple'
          ? correctAnswer.correctOptionId || []
          : correctAnswer.correctOptionIds || [];

        return (
          <div className="space-y-2">
            <p className="text-sm font-medium text-green-700">Correct Answer(s):</p>
            <div className="space-y-1">
              {options.map((option: any, index: number) => {
                const optionId = option.public_id || option.publicId || `option-${index}`;
                const isCorrect = correctOptionIds.includes(optionId);
                
                return (
                  <div
                    key={optionId}
                    className={cn(
                      'p-2 rounded border text-sm',
                      isCorrect 
                        ? 'bg-green-50 border-green-200 text-green-800' 
                        : 'bg-gray-50 border-gray-200 text-gray-600'
                    )}
                  >
                    <div className="flex items-center gap-2">
                      {isCorrect ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <div className="h-4 w-4" />
                      )}
                      {option.image_url ? (
                        <div className="flex items-center gap-2">
                          <img 
                            src={option.image_url} 
                            alt={option.text || `Option ${index + 1}`}
                            className="w-8 h-8 object-contain rounded"
                          />
                          {option.text && <span>{option.text}</span>}
                        </div>
                      ) : (
                        <span>{option.text || `Option ${index + 1}`}</span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        );
      }

      case 'input':
        return (
          <div className="space-y-2">
            <p className="text-sm font-medium text-green-700">Correct Answer:</p>
            <div className="p-3 bg-green-50 border border-green-200 rounded">
              <p className="text-sm text-green-800 font-medium">
                {correctAnswer.correctAnswer || 'No answer provided'}
              </p>
            </div>
          </div>
        );

      case 'true-false':
        return (
          <div className="space-y-2">
            <p className="text-sm font-medium text-green-700">Correct Answer:</p>
            <div className="p-3 bg-green-50 border border-green-200 rounded">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <p className="text-sm text-green-800 font-medium">
                  {typeof correctAnswer.isTrue === 'boolean'
                    ? (correctAnswer.isTrue ? 'True' : 'False')
                    : 'No answer provided'
                  }
                </p>
              </div>
            </div>
          </div>
        );

      case 'cloze':
        const answers = correctAnswer.correctAnswers || [];
        const textParts = exerciseData.text_parts || exerciseData.textParts || [];

        return (
          <div className="space-y-4">
            {/* Complete text with answers filled in */}
            {textParts.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-green-700">Complete Text with Answers:</p>
                <div className="p-3 bg-green-50 border border-green-200 rounded">
                  <div className="text-sm text-green-800">
                    {textParts.map((part: string, index: number) => (
                      <span key={index}>
                        {part}
                        {index < textParts.length - 1 && (
                          <span className="inline-block mx-1 px-2 py-1 bg-green-200 text-green-900 rounded font-medium">
                            {answers[index] || '[No answer]'}
                          </span>
                        )}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Individual answers list */}
            <div className="space-y-2">
              <p className="text-sm font-medium text-green-700">Individual Answers:</p>
              <div className="space-y-1">
                {answers.map((answer: string, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <span className="text-xs text-gray-500 w-16">Blank {index + 1}:</span>
                    <div className="p-2 bg-green-50 border border-green-200 rounded flex-1">
                      <p className="text-sm text-green-800">{answer || 'No answer'}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'dropdown':
        const selections = correctAnswer.correctSelections || [];
        return (
          <div className="space-y-2">
            <p className="text-sm font-medium text-green-700">Correct Selections:</p>
            <div className="space-y-1">
              {selections.map((selection: string, index: number) => (
                <div key={index} className="flex items-center gap-2">
                  <span className="text-xs text-gray-500 w-20">Dropdown {index + 1}:</span>
                  <div className="p-2 bg-green-50 border border-green-200 rounded flex-1">
                    <p className="text-sm text-green-800">{selection || 'No selection'}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'highlight':
        const parts = exerciseData.parts || [];
        const correctIndices = correctAnswer.correctIndices || [];
        return (
          <div className="space-y-2">
            <p className="text-sm font-medium text-green-700">Parts to Highlight:</p>
            <div className="p-3 bg-gray-50 border rounded">
              <div className="flex flex-wrap gap-1">
                {parts.map((part: string, index: number) => (
                  <span
                    key={index}
                    className={cn(
                      'px-2 py-1 rounded text-sm',
                      correctIndices.includes(index)
                        ? 'bg-yellow-200 text-yellow-800'
                        : 'bg-gray-200 text-gray-700'
                    )}
                  >
                    {part}
                  </span>
                ))}
              </div>
            </div>
          </div>
        );

      case 'matching-pairs':
        const columnA = exerciseData.columnA || exerciseData.column_a || [];
        const columnB = exerciseData.columnB || exerciseData.column_b || [];
        const pairs = correctAnswer.correctPairs || {};
        
        // Helper to find item by ID or generated fallback ID
        const findItemById = (items: any[], itemId: string, prefix: string) => {
          // First try to find by actual public_id
          let item = items.find((item: any) => 
            (item.public_id || item.publicId) === itemId
          );
          
          // If not found and ID looks like a generated one (e.g., 'item-a-0')
          if (!item && itemId.startsWith(prefix)) {
            const indexMatch = itemId.match(new RegExp(`^${prefix}-(\\d+)$`));
            if (indexMatch) {
              const index = parseInt(indexMatch[1], 10);
              item = items[index];
            }
          }
          
          return item;
        };
        
        return (
          <div className="space-y-2">
            <p className="text-sm font-medium text-green-700">Correct Pairs:</p>
            <div className="space-y-1">
              {Object.entries(pairs).map(([itemAId, itemBId]) => {
                const itemA = findItemById(columnA, itemAId, 'item-a');
                const itemB = findItemById(columnB, itemBId as string, 'item-b');
                
                // Add console warnings for debugging
                if (!itemA) {
                  console.warn(`Column A item with ID "${itemAId}" not found in exercise data`);
                }
                if (!itemB) {
                  console.warn(`Column B item with ID "${itemBId}" not found in exercise data`);
                }
                
                return (
                  <div key={itemAId} className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded">
                    <span className="text-sm text-green-800">
                      {itemA?.text || `[Missing: ${itemAId}]`}
                    </span>
                    <span className="text-gray-400">↔</span>
                    <span className="text-sm text-green-800">
                      {itemB?.text || `[Missing: ${itemBId}]`}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        );

      case 'categorize': {
        const categories = exerciseData.categories || [];
        const options = exerciseData.options || [];
        const categorizations = correctAnswer.correctCategories || {};
        
        // Helper to normalize IDs for comparison
        const normalizeId = (id: string): string => {
          if (!id) return '';
          // Remove all non-alphanumeric characters for comparison
          return id.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
        };

        // Helper to find option by ID with multiple fallback strategies
        const findOptionById = (optionId: string) => {
          // Log for debugging in development only
          if (process.env.NODE_ENV === 'development') {
            console.log('Looking for option with ID:', optionId);
            console.log('Available options:', options.map((opt: any) => ({
              public_id: opt.public_id,
              publicId: opt.publicId,
              text: opt.text
            })));
          }

          // Strategy 1: Try exact match
          let option = options.find((opt: any) => 
            (opt.public_id || opt.publicId) === optionId
          );
          
          // Strategy 2: Try converting underscores to hyphens
          if (!option) {
            const hyphenatedId = optionId.replace(/_/g, '-');
            option = options.find((opt: any) => 
              (opt.public_id || opt.publicId) === hyphenatedId
            );
          }

          // Strategy 3: Try converting hyphens to underscores
          if (!option) {
            const underscoredId = optionId.replace(/-/g, '_');
            option = options.find((opt: any) => 
              (opt.public_id || opt.publicId) === underscoredId
            );
          }

          // Strategy 4: Try normalized comparison (remove all special characters)
          if (!option) {
            const normalizedSearchId = normalizeId(optionId);
            option = options.find((opt: any) => {
              const optId = opt.public_id || opt.publicId || '';
              return normalizeId(optId) === normalizedSearchId;
            });
          }
          
          // Strategy 5: If ID looks like a generated one (e.g., 'option-0')
          if (!option && optionId.startsWith('option-')) {
            const indexMatch = optionId.match(/^option-(\d+)$/);
            if (indexMatch) {
              const index = parseInt(indexMatch[1], 10);
              option = options[index];
            }
          }

          // Strategy 6: Try to find by matching any ID field
          if (!option) {
            option = options.find((opt: any) => {
              // Check various possible ID fields
              const possibleIds = [
                opt.id,
                opt._id,
                opt.publicId,
                opt.public_id,
                opt.uuid,
                opt.key
              ].filter(Boolean);
              
              return possibleIds.some(id => 
                id === optionId || 
                normalizeId(id) === normalizeId(optionId)
              );
            });
          }
          
          if (!option && process.env.NODE_ENV === 'development') {
            console.warn(`Option with ID "${optionId}" not found. Tried multiple strategies.`);
          }
          
          return option;
        };

        // Helper to find category by ID with multiple fallback strategies
        const findCategoryById = (categoryId: string) => {
          // Try exact match first
          let category = categories.find((cat: any) => 
            (cat.public_id || cat.publicId) === categoryId
          );

          // Try normalized comparison
          if (!category) {
            const normalizedSearchId = normalizeId(categoryId);
            category = categories.find((cat: any) => {
              const catId = cat.public_id || cat.publicId || '';
              return normalizeId(catId) === normalizedSearchId;
            });
          }

          // Try with various ID fields
          if (!category) {
            category = categories.find((cat: any) => {
              const possibleIds = [
                cat.id,
                cat._id,
                cat.publicId,
                cat.public_id,
                cat.uuid,
                cat.key
              ].filter(Boolean);
              
              return possibleIds.some(id => 
                id === categoryId || 
                normalizeId(id) === normalizeId(categoryId)
              );
            });
          }

          return category;
        };

        // Group items by their correct categories
        const categoryGroups: { [key: string]: { category: any; items: string[] } } = {};
        
        Object.entries(categorizations).forEach(([itemId, catId]) => {
          const category = findCategoryById(catId as string);
          const item = findOptionById(itemId);
          
          if (category && item) {
            const categoryKey = category.public_id || category.publicId || catId;
            if (!categoryGroups[categoryKey]) {
              categoryGroups[categoryKey] = {
                category,
                items: []
              };
            }
            categoryGroups[categoryKey].items.push(item.text || '[Unnamed option]');
          } else {
            // Log detailed information for debugging in development only
            if (process.env.NODE_ENV === 'development') {
              if (!category) {
                console.warn(`Category with ID "${catId}" not found`);
              }
              if (!item) {
                console.warn(`Item with ID "${itemId}" not found`);
              }
            }
          }
        });
        
        return (
          <div className="space-y-2">
            <p className="text-sm font-medium text-green-700">Correct Categories:</p>
            <div className="space-y-2">
              {Object.values(categoryGroups).map((group, index) => {
                const category = group.category;

                return (
                  <div key={`category-${index}`} className="p-2 bg-green-50 border border-green-200 rounded">
                    <p className="text-sm font-medium text-green-800">{category.text || `Category ${index + 1}`}</p>
                    {group.items.length > 0 ? (
                      <p className="text-xs text-green-600 mt-1">
                        {group.items.join(', ')}
                      </p>
                    ) : (
                      <p className="text-xs text-gray-500 mt-1 italic">No items assigned</p>
                    )}
                  </div>
                );
              })}
              
              {/* Show any categories without items */}
              {categories
                .filter((cat: any) => {
                  const catId = cat.public_id || cat.publicId;
                  return !Object.values(categoryGroups).some(group => 
                    (group.category.public_id || group.category.publicId) === catId
                  );
                })
                .map((category: any, index: number) => (
                  <div key={`empty-category-${index}`} className="p-2 bg-gray-50 border border-gray-200 rounded">
                    <p className="text-sm font-medium text-gray-700">{category.text || `Category ${index + 1}`}</p>
                    <p className="text-xs text-gray-500 mt-1 italic">No items assigned</p>
                  </div>
                ))
              }
            </div>
          </div>
        );
      }

      case 'error-correction':
        const corrections = correctAnswer.corrections || [];
        return (
          <div className="space-y-2">
            <p className="text-sm font-medium text-green-700">Corrections:</p>
            <div className="space-y-1">
              {corrections.map((correction: any, index: number) => (
                <div key={index} className="p-2 bg-green-50 border border-green-200 rounded">
                  <p className="text-sm text-green-800">
                    <span className="font-medium">Part {correction.index}:</span> {correction.text}
                  </p>
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return (
          <Alert>
            <AlertDescription>
              Solution preview not implemented for exercise type: {exerciseType}
            </AlertDescription>
          </Alert>
        );
    }
  };

  return (
    <Card className={cn('', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-base">
          <Eye className="h-4 w-4" />
          Solution Preview
          <Badge variant="outline" className="ml-auto">
            Student View
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Correct Answer Section */}
        <div>
          {renderCorrectAnswer()}
        </div>

        <Separator />

        {/* Solution Steps Section */}
        {hasSteps && (
          <div className="space-y-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSteps(!showSteps)}
              className="w-full justify-between"
            >
              <div className="flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                <span>Solution Steps ({solutionData.solutionSteps?.length})</span>
              </div>
              {showSteps ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>

            {showSteps && (
              <div className="space-y-3 mt-3">
                {solutionData.solutionSteps?.map((step, index) => (
                  <div key={index} className="p-3 bg-blue-50 border border-blue-200 rounded">
                    <div className="flex items-start gap-2">
                      <Badge variant="secondary" className="text-xs">
                        Step {index + 1}
                      </Badge>
                      <div className="flex-1 space-y-2">
                        {step.text && (
                          <p className="text-sm text-blue-800">{step.text}</p>
                        )}
                        {step.math && (
                          <div className="p-2 bg-white border rounded text-sm">
                            <LatexRenderer content={step.math} fontSize="sm" />
                          </div>
                        )}
                        {step.imagePublicId && (
                          <div className="text-xs text-blue-600">
                            Image: {step.imagePublicId}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Video Section */}
        {hasVideo && (
          <div className="space-y-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowVideo(!showVideo)}
              className="w-full justify-between"
            >
              <div className="flex items-center gap-2">
                <Video className="h-4 w-4" />
                <span>Video Explanation</span>
              </div>
              <Play className="h-4 w-4" />
            </Button>
            
            {showVideo && (
              <div className="p-3 bg-purple-50 border border-purple-200 rounded">
                <p className="text-sm text-purple-800 mb-2">Video ID: {solutionData.videoPublicId}</p>
                <p className="text-xs text-purple-600">
                  Students will see a video player here with the solution explanation.
                </p>
              </div>
            )}
          </div>
        )}

        {/* Empty State */}
        {!solutionData.correctAnswer && !hasSteps && !hasVideo && (
          <div className="text-center py-8 text-gray-500">
            <Eye className="h-12 w-12 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No solution configured</p>
            <p className="text-xs mt-1">Configure the solution to see how it will appear to students</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
