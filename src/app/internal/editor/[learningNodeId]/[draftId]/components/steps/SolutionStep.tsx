import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { SolutionEditorPanel } from '../panels/SolutionEditorPanel';
import { SolutionPreviewPanel } from '../panels/SolutionPreviewPanel';
import { SolutionStepProps } from '../../types';
import { DEFAULT_PANEL_SIZE, MIN_PANEL_SIZE } from '../../constants';
import { transformExerciseData } from '../../utils/transformExerciseData';

export function SolutionStep({
  draft,
  updateSolutionData,
  solutionEditorMode,
  onEditorModeChange
}: SolutionStepProps) {
  // Transform exercise data from backend format to frontend format
  const transformedExerciseData = transformExerciseData(draft.exerciseType, draft.dataJson);

  return (
    <div className="h-full min-h-[600px]">
      <ResizablePanelGroup direction="horizontal" className="h-full">
        {/* Solution Editor */}
        <ResizablePanel defaultSize={DEFAULT_PANEL_SIZE} minSize={MIN_PANEL_SIZE}>
          <SolutionEditorPanel
            exerciseType={draft.exerciseType}
            exerciseData={transformedExerciseData}
            solutionData={draft.solutionJson}
            onChange={updateSolutionData}
            mode={solutionEditorMode}
            onModeChange={onEditorModeChange}
          />
        </ResizablePanel>

        <ResizableHandle withHandle />

        {/* Solution Preview */}
        <ResizablePanel defaultSize={DEFAULT_PANEL_SIZE} minSize={MIN_PANEL_SIZE}>
          <SolutionPreviewPanel
            exerciseType={draft.exerciseType}
            exerciseData={transformedExerciseData}
            solutionData={draft.solutionJson}
          />
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}