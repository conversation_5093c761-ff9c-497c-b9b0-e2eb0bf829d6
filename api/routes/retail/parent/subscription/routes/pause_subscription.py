from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account, ActiveSubscription
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.response import PauseSubscriptionResponse
import stripe
import os
from loguru import logger
from datetime import datetime
from .pause_confirmation_utils.send_pause_confirmation_email import send_pause_confirmation_email

# Hard-coded pause end date: September 15, 2025 00:00 UTC (**********)
PAUSE_END_TIMESTAMP = **********
PAUSE_END_DATE = datetime.fromtimestamp(PAUSE_END_TIMESTAMP)

stripe.api_key = os.environ.get('STRIPE_API_KEY')

router = APIRouter()
base_url = "/api/app/parent/subscription"


@router.post(base_url + '/pause', status_code=200, response_model=PauseSubscriptionResponse)
def pause_subscription(
    request: Request, 
    x_auth_parent_token: str = Header(None),  # type: ignore
    has_valid_token: bool = Depends(has_valid_parent_token),  # type: ignore
    db: Session = Depends(get_db)
):
    """
    Pauses all active subscriptions for the authenticated parent.
    - Monthly subscriptions: Pauses payment collection until September 15, 2025
    - Yearly subscriptions: Adds trial period until September 15, 2025
    """
    parent_public_id = request.state.parent_uuid
    
    if not parent_public_id:
        raise CustomException(
            "Not authorized!", 
            "No parent_account_public_id in token",
            status_code=401, 
            error_type="permission_error"
        )

    # Get the parent account
    relevant_account = db.query(Account).filter(
        Account.account_cognito_id == parent_public_id
    ).first()
    
    if not relevant_account:
        raise CustomException(
            "Not authorized!", 
            "No corresponding Account found",
            status_code=401, 
            error_type="permission_error"
        )

    # Get Stripe customer subscriptions (using Stripe as source of truth)
    try:
        stripe_subscriptions = stripe.Subscription.list(
            customer=relevant_account.account_stripe_id,
            status='active'
        )
    except Exception as e:
        logger.error(f"Failed to fetch Stripe subscriptions: {e}")
        raise CustomException(
            "Stripe error", 
            "Failed to fetch subscription data from Stripe",
            status_code=500, 
            error_type="external_service_error"
        )

    if not stripe_subscriptions.data:
        raise CustomException(
            "No active subscriptions", 
            "No active subscriptions found to pause",
            status_code=400, 
            error_type="validation_error"
        )

    paused_subscriptions = []
    failed_subscriptions = []

    for stripe_sub in stripe_subscriptions.data:
        try:
            # Get price information from Stripe subscription
            price_id = stripe_sub['items']['data'][0]['price']['id']
            price = stripe.Price.retrieve(price_id)
            interval = price['recurring']['interval']  # 'month' or 'year'
            
            # Determine subscription type and apply appropriate pause logic
            if interval == 'month':
                # For monthly: pause payment collection
                stripe.Subscription.modify(
                    stripe_sub['id'],
                    pause_collection={
                        'behavior': 'mark_uncollectible',
                        'resumes_at': PAUSE_END_TIMESTAMP
                    }
                )
                logger.info(f"Paused monthly subscription {stripe_sub['id']} until {PAUSE_END_DATE}")
                subscription_type = 'monthly'
                
            elif interval == 'year':
                # For yearly: extend subscription by freeze length for everyone
                current_period_end = stripe_sub['current_period_end']
                freeze_length = PAUSE_END_TIMESTAMP - datetime.now().timestamp()
                extended_trial_end = current_period_end + freeze_length
                
                stripe.Subscription.modify(
                    stripe_sub['id'],
                    trial_end=int(extended_trial_end),
                    proration_behavior='none'
                )
                logger.info(f"Extended yearly subscription {stripe_sub['id']} by {freeze_length/86400:.1f} days (current_period_end: {datetime.fromtimestamp(current_period_end)} → trial_end: {datetime.fromtimestamp(extended_trial_end)})")
                subscription_type = 'yearly'
            else:
                logger.warning(f"Unknown subscription interval '{interval}' for subscription {stripe_sub['id']}")
                failed_subscriptions.append(f"Subscription {stripe_sub['id']} (unknown interval)")
                continue

            paused_subscriptions.append({
                'subscription_type': subscription_type,
                'stripe_subscription_id': stripe_sub['id'],
                'price_id': price_id,
                'interval': interval
            })

        except Exception as e:
            logger.error(f"Failed to pause subscription {stripe_sub['id']}: {e}")
            failed_subscriptions.append(f"Subscription {stripe_sub['id']}")

    if paused_subscriptions and not failed_subscriptions:
        message = f"Successfully paused {len(paused_subscriptions)} subscription(s) until {PAUSE_END_DATE.strftime('%B %d, %Y')}"
        success = True
        
        # Update database: set all active subscriptions for this account to 'paused'
        try:
            updated_count = db.query(ActiveSubscription).filter(
                ActiveSubscription.account_id == relevant_account.account_id,
                ActiveSubscription.status == 'active'
            ).update({'status': 'paused'})
            
            db.commit()
            logger.info(f"Updated {updated_count} ActiveSubscription records to 'paused' status for account {relevant_account.account_id}")
            
        except Exception as e:
            logger.error(f"Failed to update database subscription status: {e}")
            db.rollback()
            # Don't fail the entire operation if database update fails, since Stripe changes are already applied
            logger.warning("Stripe subscriptions were paused successfully, but database update failed")
        
        # Send confirmation email for successful pause
        try:
            formatted_resume_date = PAUSE_END_DATE.strftime('%B %d, %Y')
            email_sent = send_pause_confirmation_email(
                email_address=relevant_account.account_email,
                language=relevant_account.account_language or 'en',
                resume_date=formatted_resume_date,
                paused_count=len(paused_subscriptions)
            )
            if email_sent:
                logger.info(f"Pause confirmation email sent to {relevant_account.account_email}")
            else:
                logger.warning(f"Failed to send pause confirmation email to {relevant_account.account_email}")
        except Exception as e:
            logger.error(f"Error sending pause confirmation email: {e}")
            # Don't fail the entire operation if email fails
            
    elif paused_subscriptions and failed_subscriptions:
        message = f"Paused {len(paused_subscriptions)} subscription(s) but failed to pause {len(failed_subscriptions)} subscription(s)"
        success = False
    else:
        message = "Failed to pause any subscriptions"
        success = False

    return PauseSubscriptionResponse(
        success=success,
        message=message,
        paused_subscriptions=paused_subscriptions,
        failed_subscriptions=failed_subscriptions,
        resume_date=PAUSE_END_DATE.isoformat()
    )