from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account, Toast, DismissedToast, ChildAccount
from utils.auth import has_valid_token
from utils.exceptions import CustomException

from ..models.request import DismissToastRequest

router = APIRouter()
base_url = '/api/app/shared'


@router.post(base_url + '/dismiss-toast', status_code=200)
def dismiss_toast(
    request: Request,
    dismissed_toast: DismissToastRequest,
    x_auth_parent_token: str = Header(None),
    has_valid_token: bool = Depends(has_valid_token),
    db: Session = Depends(get_db)
):
    parent_public_id = request.state.parent_uuid
    child_public_id = request.state.decoded_child_token

    if parent_public_id:
        account = db.query(Account).filter(
            Account.account_cognito_id == parent_public_id).first()
        if not account:
            raise CustomException("Parent account not found", status_code=404)

        toast = db.query(Toast).filter(
            Toast.toast_public_id == dismissed_toast.toast_public_id).first()
        if toast and toast.permanently_dismissable:
            dismissed_toast = DismissedToast(
                account_id=account.account_id,
                toast_id=toast.toast_id
            )
            db.add(dismissed_toast)

        db.commit()
        return {"message": "Toasts dismissed successfully for parent account"}

    elif child_public_id:
        child_account = db.query(ChildAccount).filter(
            ChildAccount.child_account_public_id == child_public_id).first()
        if not child_account:
            raise CustomException("Child account not found", status_code=404)

        toast = db.query(Toast).filter(
            Toast.toast_public_id == dismissed_toast.toast_public_id).first()
        if toast and toast.permanently_dismissable:
            dismissed_toast = DismissedToast(
                child_account_id=child_account.child_account_id,
                toast_id=toast.toast_id
            )
            db.add(dismissed_toast)

        db.commit()
        return {"message": "Toasts dismissed successfully for child account"}

