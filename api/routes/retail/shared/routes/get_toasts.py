from fastapi import API<PERSON><PERSON><PERSON>, Depen<PERSON>, Header, Request
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from db.database import get_db
from db.models import Account, Toast, DismissedToast, ChildAccount, Trial, ActiveSubscription
from utils.auth import has_valid_token
from utils.exceptions import CustomException
from sqlalchemy import not_
import json
from datetime import datetime
from sqlalchemy import select
from loguru import logger

router = APIRouter()
base_url = '/api/app/shared'


FREE_ACCESS = False

# If it is the parent, we need to check the subscription status, if the user is not subscribed, we need to check for the trial status

def prepare_trial_toast(relevant_account, toasts, db, parent_account, account_type='parent'):
    # logger.debug(f"Entering prepare_trial_toast function. Account type: {account_type}")
    relevant_trial = None
    if account_type == 'parent':
        # logger.debug("Querying trial for parent account")
        relevant_trial = db.query(Trial).filter(
            Trial.trial_account_id == relevant_account.account_id,
            Trial.trial_status != 'invalid'
        ).first()
    elif account_type == 'child':
        # logger.debug("Querying trial for child account")
        if parent_account:
            # logger.debug("Parent account found, querying trials for both child and parent")
            relevant_trial = db.query(Trial).filter(
                or_(Trial.trial_child_account_id == relevant_account.child_account_id,
                    Trial.trial_account_id == parent_account.account_id),
                Trial.trial_status != 'invalid'
            ).first()
        else:
            # logger.debug("No parent account, querying trial for child only")
            relevant_trial = db.query(Trial).filter(
                Trial.trial_child_account_id == relevant_account.child_account_id,
                Trial.trial_status != 'invalid'
            ).first()
    else:
        logger.error(f"Invalid account type: {account_type}")
        raise CustomException("Invalid account type", status_code=400)

    # logger.debug(f"Relevant trial found: {relevant_trial is not None}")

    if relevant_trial:
        remaining_days = relevant_trial.trial_end_date - datetime.utcnow()
        # logger.debug(f"Trial remaining days: {remaining_days.days}")

        if remaining_days.days > 0 and relevant_trial.trial_status == 'active':
            # logger.debug("Trial is active and has remaining days")
            if account_type == 'parent':
                relevant_toasts = toasts.filter(
                    Toast.type != 'trial-expired'
                )
                trial_active_toast = toasts.filter(
                    Toast.type == 'trial-active'
                ).first()
            elif account_type == 'child':
                relevant_toasts = toasts.filter(
                    Toast.type != 'trial-expired-child'
                )
                trial_active_toast = toasts.filter(
                    Toast.type == 'trial-active-child'
                ).first()

            if trial_active_toast:
                # logger.debug("Updating trial active toast content")
                trial_active_toast_content = json.loads(trial_active_toast.content)
                languages = trial_active_toast_content.keys()
                for language in languages:
                    # logger.debug(f"Updating content for language: {language}")
                    if remaining_days.days > 1:
                        day_string = {'en': 'days', 'de': 'Tage', 'fr': 'jours', 'lu': 'Deeg'}.get(language, 'days')
                    else:
                        day_string = {'en': 'day', 'de': 'Tag', 'fr': 'jour', 'lu': 'Dag'}.get(language, 'day')
                    trial_active_toast_content[language]['description'] = trial_active_toast_content[language]['description'].replace(
                        'PLACEHOLDER', f"{remaining_days.days} {day_string}"
                    )
                trial_active_toast.content = json.dumps(trial_active_toast_content)

                # Remove the existing trial-active toast from the query
                if account_type == 'parent':
                    relevant_toasts = relevant_toasts.filter(
                        Toast.type != 'trial-active'
                    )
                elif account_type == 'child':
                    relevant_toasts = relevant_toasts.filter(
                        Toast.type != 'trial-active-child'
                    )

                # logger.debug("Executing query to get all other toasts")
                toasts_list = relevant_toasts.all()

                # logger.debug("Appending updated trial_active_toast")
                toasts_list.append(trial_active_toast)

                # logger.debug("Sorting toasts by priority")
                toasts_list.sort(key=lambda x: x.priority, reverse=True)

                return toasts_list
        else:
            # logger.debug("Trial has ended or is not active")
            if relevant_trial.trial_status != 'expired':
                # logger.debug("Updating trial status to expired")
                relevant_trial.trial_status = 'expired'
                db.commit()
            # Filter out the trial-active toast from the relevant toasts
            if account_type == 'parent':
                relevant_toasts = toasts.filter(
                    Toast.type != 'trial-active'
                )

            elif account_type == 'child':
                relevant_toasts = toasts.filter(
                    Toast.type != 'trial-active-child'
                )
            toasts = relevant_toasts.all()
            # logger.debug(f"Toasts after filtering: {[toast.type for toast in toasts]}")
        return toasts
    else:
        # logger.debug("No relevant trial found, filtering out all trial-related toasts")
        relevant_toasts = toasts.filter(
            and_(
                Toast.type != 'trial-active',
                Toast.type != 'trial-active-child',
                Toast.type != 'trial-expired',
                Toast.type != 'trial-expired-child'
            )
        )
        return relevant_toasts.all()


@router.get(base_url + '/get-toasts', status_code=200)
def get_chapter(request: Request,  x_auth_parent_token: str = Header(None), x_auth_child_token: str = Header(None), has_valid_token: bool = Depends(has_valid_token), db: Session = Depends(get_db)):
    child_public_id = None
    parent_public_id = None

    parent_public_id = request.state.parent_uuid

    if request.state.decoded_child_token:
        child_public_id = request.state.decoded_child_token['child_account_public_id']
    if parent_public_id and child_public_id:
        parent_public_id = None

    if parent_public_id:
        relevant_account = db.query(Account).filter_by(account_cognito_id=parent_public_id).first()
        relevant_subscriptions = db.query(ActiveSubscription).filter_by(account_id=relevant_account.account_id, status='active').all()

        if not relevant_account:
            raise CustomException("Account not found", status_code=404)

        if relevant_subscriptions:
            is_subscribed = True
        else:
            is_subscribed = False

        # Subquery to get dismissed toast IDs
        dismissed_toast_subquery = select(DismissedToast.toast_id).where(
            DismissedToast.account_id == relevant_account.account_id
        ).scalar_subquery()

        # Main query to get relevant toasts
        relevant_toasts = db.query(Toast).filter(
            Toast.is_active == True,
            Toast.account_type == 'parent',
            not_(Toast.toast_id.in_(dismissed_toast_subquery))
        ).order_by(Toast.priority.desc())

        # Apply subscription filters
        if is_subscribed:
            relevant_toasts = relevant_toasts.filter(
                Toast.hide_from_subscribers == False).all()
        else:
            relevant_toasts = relevant_toasts.filter(
                Toast.show_to_subscribers_only == False)
            # In the toasts to non-subscribers there is the trial-active toast and the trial-expired toast
            relevant_toasts = prepare_trial_toast(
                relevant_account, relevant_toasts, db, relevant_account, account_type='parent')
        # Order by priority
        if FREE_ACCESS:
            relevant_toasts = [toast for toast in relevant_toasts if toast.type not in ['trial-active', 'trial-expired']]
            
        return {
            'toasts': [{
                'toast_public_id': toast.toast_public_id,
                'content': json.loads(toast.content),
                'priority': toast.priority,
                'temporary_dismissable': toast.temporary_dismissable,
                'permanently_dismissable': toast.permanently_dismissable,
                'type': toast.type,
            } for toast in relevant_toasts]
        }
    if child_public_id:
        # Combine the two queries into a single join operation
        relevant_child_account = db.query(ChildAccount).filter(
            ChildAccount.child_account_public_id == child_public_id).first()

        if not relevant_child_account:
            raise CustomException("Child account not found", status_code=404)

            # Subquery to get dismissed toast IDs
        dismissed_toast_subquery = select(DismissedToast.toast_id).where(
            DismissedToast.child_account_id == relevant_child_account.child_account_id
        ).scalar_subquery()

        # Main query to get relevant toasts
        relevant_toasts = db.query(Toast).filter(
            Toast.is_active == True,
            Toast.account_type == 'child',
            not_(Toast.toast_id.in_(dismissed_toast_subquery))
        ).order_by(Toast.priority.desc())

        relevant_parent_account = relevant_child_account.parent_account
        # Check if the parent is subscribed, if so we filter out the trial toasts, of not we need to check if the child has a trial and show the relevant toast
        if relevant_parent_account:
            relevant_subscriptions = db.query(ActiveSubscription).filter_by(account_id=relevant_parent_account.account_id, status='active').all()
            if relevant_subscriptions:
                is_subscribed = True
            else:
                is_subscribed = False
          
        if is_subscribed:
            relevant_toasts = relevant_toasts.filter(
                Toast.hide_from_subscribers == False)
        else:
            relevant_toasts = relevant_toasts.filter(
                Toast.show_to_subscribers_only == False)
            relevant_toasts = prepare_trial_toast(
                relevant_child_account, relevant_toasts, db, relevant_parent_account, account_type='child')

        if FREE_ACCESS:
            relevant_toasts = [toast for toast in relevant_toasts if toast.type not in ['trial-active-child', 'trial-expired-child']]

        return {
            'toasts': [{
                'toast_public_id': toast.toast_public_id,
                'content': json.loads(toast.content),
                'priority': toast.priority,
                'temporary_dismissable': toast.temporary_dismissable,
                'permanently_dismissable': toast.permanently_dismissable,
                'type': toast.type,
            } for toast in relevant_toasts]
        }
    raise CustomException("Invalid account type", status_code=400)
