'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertTriangle, Plus, Trash2 } from 'lucide-react';

interface ClozeSolutionEditorProps {
  exerciseData: Record<string, any>;
  correctAnswer: any;
  onChange: (correctAnswer: any) => void;
}

export function ClozeSolutionEditor({
  exerciseData,
  correctAnswer,
  onChange
}: ClozeSolutionEditorProps) {
  const [answers, setAnswers] = useState<string[]>([]);

  const textParts = exerciseData.text_parts || exerciseData.textParts || [];
  const blanksCount = Math.max(0, textParts.length - 1); // Number of blanks between text parts

  // Initialize answers from correct answer
  useEffect(() => {
    if (correctAnswer?.correctAnswers && Array.isArray(correctAnswer.correctAnswers)) {
      setAnswers(correctAnswer.correctAnswers);
    } else {
      // Initialize with empty answers for each blank
      setAnswers(new Array(blanksCount).fill(''));
    }
  }, [correctAnswer, blanksCount]);

  const handleAnswerChange = (index: number, value: string) => {
    const newAnswers = [...answers];
    newAnswers[index] = value;
    setAnswers(newAnswers);
    onChange({
      correctAnswers: newAnswers
    });
  };

  const addAnswer = () => {
    const newAnswers = [...answers, ''];
    setAnswers(newAnswers);
    onChange({
      correctAnswers: newAnswers
    });
  };

  const removeAnswer = (index: number) => {
    const newAnswers = answers.filter((_, i) => i !== index);
    setAnswers(newAnswers);
    onChange({
      correctAnswers: newAnswers
    });
  };

  if (textParts.length === 0) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No text parts found in exercise data. Please add text parts to the exercise first.
        </AlertDescription>
      </Alert>
    );
  }

  const filledAnswersCount = answers.filter(answer => answer.trim()).length;

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-base font-semibold">
        <CheckCircle className="h-4 w-4" />
        Fill-in-the-Blank Answers
      </div>
      <p className="text-sm text-gray-600">
            Enter the correct answers for each blank in the cloze exercise:
          </p>

          {/* Preview of the exercise with blanks */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="text-sm font-medium mb-2">Exercise Preview:</p>
            <div className="text-sm">
              {textParts.map((part: string, index: number) => (
                <span key={index}>
                  {part}
                  {index < textParts.length - 1 && (
                    <span className="inline-block mx-1 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                      [Blank {index + 1}]
                    </span>
                  )}
                </span>
              ))}
            </div>
          </div>
          
          <div className="space-y-3">
            {answers.map((answer, index) => (
              <div key={index} className="flex items-center gap-2">
                <Label className="w-20 text-sm">Blank {index + 1}:</Label>
                <Input
                  value={answer}
                  onChange={(e) => handleAnswerChange(index, e.target.value)}
                  placeholder={`Answer for blank ${index + 1}...`}
                  className="flex-1"
                />
                {answers.length > blanksCount && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeAnswer(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>

          {answers.length < 10 && (
            <Button onClick={addAnswer} variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Answer
            </Button>
          )}

          <div className="flex items-center gap-2">
            <Badge variant={filledAnswersCount === blanksCount ? "secondary" : "outline"}>
              {filledAnswersCount} of {blanksCount} blanks filled
            </Badge>
            {blanksCount > 0 && answers.length !== blanksCount && (
              <Badge variant="destructive">
                Answer count mismatch
              </Badge>
            )}
          </div>

          {filledAnswersCount > 0 && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                {filledAnswersCount} answer(s) configured for the blanks.
              </AlertDescription>
            </Alert>
          )}

          {filledAnswersCount === 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please enter at least one correct answer.
              </AlertDescription>
            </Alert>
          )}

          <div className="text-xs text-gray-500">
            <p><strong>Note:</strong> Answer comparison is case-sensitive and requires exact match.</p>
          </div>
    </div>
  );
}
