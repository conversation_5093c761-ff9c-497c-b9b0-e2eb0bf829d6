from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header,  Request
from sqlalchemy.orm import Session
from sqlalchemy import or_
from db.database import get_db
from db.models import Account,  Trial, SubscriptionOption, ActiveSubscription
import uuid
import os
import stripe
from utils.exceptions import CustomException
import json
import boto3
from loguru import logger
from datetime import datetime
from typing import Optional

from api.routes.retail.utils.marketing_utils import remove_from_subscribers_list, handle_post_purchase_email
# Initialize AWS SES client
ses_client = boto3.client(
    'ses',
    region_name='eu-central-1',  # replace with your AWS region
    aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
    aws_secret_access_key=os.environ['AWS_SECRET_KEY']
)


SES_EMAIL_SOURCE = os.environ.get('SES_EMAIL_SOURCE')
SES_EMAIL_DESTINATION = os.environ.get('SES_EMAIL_DESTINATION')


def send_status_email(status, message=None):
    subject_map = {
        "success": "New subscription created",
        "update": "Subscription updated",
        "failure": "Issue while creating new subscription",
        "cancel": "Customer has cancelled subscription",
        "refund_failure": f"Could not issue refund \n{message}",
        "fraud": f"Fraud warning issued for charge! \n{message}",
    }

    subject = subject_map.get(status, "Notification")
    body_text = subject
    body_html = subject  # Assuming HTML content is the same as text for simplification

    try:
        response = ses_client.send_email(
            Source=SES_EMAIL_SOURCE,
            Destination={'ToAddresses': [SES_EMAIL_DESTINATION]},
            Message={
                'Body': {
                    'Text': {'Data': body_text, 'Charset': 'UTF-8'},
                    'Html': {'Data': body_html, 'Charset': 'UTF-8'}
                },
                'Subject': {'Data': subject, 'Charset': 'UTF-8'}
            }
        )
    except Exception as e:
        logger.error(f"Error sending contact email: {str(e)}")
        pass


# This is your test secret API key.
stripe.api_key = os.environ.get('STRIPE_API_KEY')

router = APIRouter()
base_url = "/api/subscriptions"
# Import aws SES and send email when new subscription is created


def handle_session_completed(event, db: Session):
    session = event["data"]["object"]
    customer_id = session["customer"]
    charge_id = None

    logger.info(f"Handling completed checkout session for customer: {customer_id}")
    logger.debug(f"Full session completed event data: {json.dumps(event, indent=2)}")

    try:
        checkout_session = stripe.checkout.Session.retrieve(
            session['id'], expand=["line_items"])
        line_items = checkout_session["line_items"]['data']
        if not line_items:
            raise ValueError("No line items in checkout session")

        price_id = line_items[0]['price']['id']
        # Get the charge ID from the checkout session
        charge_id = checkout_session["payment_intent"]

        relevant_account = db.query(Account).filter_by(
            account_stripe_id=customer_id).first()
        if not relevant_account:
            raise ValueError("No corresponding account found")

        relevant_subscription_option = db.query(SubscriptionOption).filter( or_(
            SubscriptionOption.stripe_monthly_id == price_id,
            SubscriptionOption.stripe_yearly_id == price_id)).first()
        
        # To determine the subscription type
        subscription_type = "monthly" if relevant_subscription_option.stripe_monthly_id == price_id else "yearly"

        if not relevant_subscription_option:
            raise ValueError("No corresponding subscription found")

        # Check if there already is an active subscription for this account and this specific subscription option
        relevant_active_subscription = db.query(ActiveSubscription).filter_by(
            subscription_option_id=relevant_subscription_option.subscription_option_id,
            account_id=relevant_account.account_id).first()

        if relevant_active_subscription:
            # We change the subscription status to active
            relevant_active_subscription.status = "active"
            db.commit()
            logger.info(f"Updated existing subscription association for account {relevant_account.account_id}")

        # If there is no association, create it
        # subscription_type = relevant_subscription_option.subscription_type
        if not relevant_active_subscription:
            new_subscription_association = ActiveSubscription(
                subscription_option_id=relevant_subscription_option.subscription_option_id,
                subscription_type=subscription_type,
                account_id=relevant_account.account_id,
                status="active")

            db.add(new_subscription_association)

        # Check for any existing trials and remove them
        relevant_trial = db.query(Trial).filter_by(
            trial_account_id=relevant_account.account_id).all()
        if relevant_trial:
            for trial in relevant_trial:
                db.delete(trial)

        db.commit()

        logger.info(f"Successfully created subscription association for account {relevant_account.account_id}")
        logger.info(f"Removed trial (if existed) for account {relevant_account.account_id}")
        try:
            send_status_email("success")
            handle_post_purchase_email(relevant_account.account_email, relevant_account.account_language)
        except Exception as e:
            logger.error(f"Error sending post-purchase email: {e}")
        
    except Exception as e:
        # Rollback the database session in case of any errors
        db.rollback()

        if charge_id:
            # Attempt to issue a refund
            try:
                stripe.Refund.create(charge=charge_id)
                send_status_email(
                    "refund_failure", f"Refund issued for charge {charge_id} due to error: {str(e)}")
            except Exception as refund_error:
                send_status_email(
                    "refund_failure", f"Failed to issue refund for charge {charge_id}: {str(refund_error)}")

        # It's generally a good idea to re-raise the exception to not silently swallow it
        # You might want to customize this part based on how you handle errors in your application
        raise CustomException(
            "Error!", f"Something went wrong while creating a new subscription: {e}",)
        # raise CustomException(status_code=500, detail=str(e))


def handle_subscription_deleted(event, db: Session):
    session = event["data"]["object"]
    customer_id = session["customer"]
    logger.info(f"Handling subscription deletion for customer: {customer_id}")
    logger.debug(f"Full subscription deleted event data: {json.dumps(event, indent=2)}")

    try:
        price_id = session['items']['data'][0]['price']['id']
        relevant_account = db.query(Account).filter_by(
            account_stripe_id=customer_id).first()
        if not relevant_account:
            raise ValueError("No corresponding account found")

        relevant_subscription_option = db.query(SubscriptionOption).filter( or_(
            SubscriptionOption.stripe_monthly_id == price_id,
            SubscriptionOption.stripe_yearly_id == price_id)).first()
        
        if not relevant_subscription_option:
            raise ValueError("No corresponding subscription found")

        # Attempt to find and delete the subscription active Subscription
        relevant_active_subscription = db.query(ActiveSubscription).filter_by(
            subscription_option_id=relevant_subscription_option.subscription_option_id,
            account_id=relevant_account.account_id).first()

        if relevant_active_subscription:
            db.delete(relevant_active_subscription)
            db.commit()
            logger.info(f"Successfully deleted subscription association for account {relevant_account.account_id}")
            send_status_email("cancel")
            remove_from_subscribers_list(relevant_account.account_email)
        else:
            raise ValueError("No corresponding subscription association found")

    except Exception as e:
        db.rollback()  # Ensure database integrity by rolling back on error

        # Log the error or send it to an error tracking system
        # Consider re-raising the exception or handling it appropriately
        raise CustomException(
            "Error!", f"Something went wrong while deleting subscription: {e}",)


def handle_early_fraud_warning(event, db: Session):
    efw = event["data"]["object"]
    logger.info("Handling early fraud warning")
    logger.debug(f"Full early fraud warning event data: {json.dumps(event, indent=2)}")

    if efw["actionable"]:
        charge_id = efw["charge"]
        try:
            logger.warning(f"Actionable fraud warning for charge {charge_id}")
            send_status_email(
                "fraud", f"Fraud warning issued for charge {charge_id}.")

        except Exception as e:
            raise CustomException(
                "Error!", f"Something went wrong while handling early fraud warning: {e}",)
        # try:
        #     # Attempt to issue a refund
        #     stripe.Refund.create(charge=charge_id)
        #     send_status_email("refund", f"Refund issued for charge {charge_id} due to early fraud warning.")
        # except Exception as refund_error:
        #     send_status_email("refund_failure", f"Failed to issue refund for charge {charge_id}: {str(refund_error)}")
        #     logger.error(f"Refund failed for charge {charge_id}: {str(refund_error)}")
        #     raise CustomException("Refund Error", f"Failed to refund charge {charge_id} after early fraud warning: {refund_error}")
    else:
        logger.info("Non-actionable fraud warning received")


def _get_account_or_log_error(db: Session, customer_id: str) -> Optional[Account]:
    """Finds the account by Stripe customer ID or logs an error."""
    if not customer_id:
        logger.error("Cannot get account: Stripe Customer ID is missing in the event.")
        return None

    relevant_account = db.query(Account).filter_by(account_stripe_id=customer_id).first()
    if not relevant_account:
        logger.error(f"No corresponding account found for customer_id: {customer_id}. Cannot process subscription update.")
        return None

    logger.info(f"Found relevant account: {relevant_account.account_id}")
    return relevant_account

def _handle_price_change(
    db: Session,
    account: Account,
    old_price_id: str,
    new_price_id: str,
    current_status: str # Pass current status to set correctly on create/update
):
    """Handles the logic when a subscription's price ID changes."""
    logger.info(f"Processing price change from {old_price_id} to {new_price_id} for account {account.account_id}.")

    relevant_old_option = db.query(SubscriptionOption).filter(or_(
        SubscriptionOption.stripe_monthly_id == old_price_id,
        SubscriptionOption.stripe_yearly_id == old_price_id)).first()

    relevant_new_option = db.query(SubscriptionOption).filter(or_(
        SubscriptionOption.stripe_monthly_id == new_price_id,
        SubscriptionOption.stripe_yearly_id == new_price_id)).first()

    if not relevant_old_option or not relevant_new_option:
         logger.error(f"Could not find one or both SubscriptionOption records for price change. Old Price: {old_price_id}, New Price: {new_price_id}")
         raise ValueError("Could not find SubscriptionOption for old or new price during price change.")

    new_subscription_type = "monthly" if relevant_new_option.stripe_monthly_id == new_price_id else "yearly"

    # --- Case 1: --- Changed frequency WITHIN the SAME Subscription Option (e.g., monthly -> yearly)
    if relevant_old_option.subscription_option_id == relevant_new_option.subscription_option_id:
     

        logger.info("Price change is within the same SubscriptionOption (e.g., monthly to yearly).")
        active_sub = db.query(ActiveSubscription).filter_by(
            subscription_option_id=relevant_old_option.subscription_option_id, # Same as new option ID here
            account_id=account.account_id).first()

        if active_sub:
            active_sub.subscription_type = new_subscription_type
            active_sub.status = current_status # Update status as well
            # Commit immediately
            db.commit()
            logger.info(f"Successfully updated ActiveSubscription {active_sub.active_subscription_id}: type to '{new_subscription_type}', status to '{current_status}'")
        else:
            # This case might indicate inconsistent state or a race condition
            logger.error(f"Attempted to update type/status for same SubscriptionOption, but no existing ActiveSubscription found for account {account.account_id}, option {relevant_old_option.subscription_option_id}")
            raise ValueError("ActiveSubscription not found during same-option price change update.")
    # --- Case 2: Changed to a DIFFERENT core Subscription Option (e.g., different year) Should not actully happen!
    else:

        logger.info("Price change involves switching to a different SubscriptionOption.")
        # Remove the old association
        delete_count = db.query(ActiveSubscription).filter_by(
            subscription_option_id=relevant_old_option.subscription_option_id,
            account_id=account.account_id).delete()
        logger.info(f"Deleted {delete_count} old ActiveSubscription record(s) for option {relevant_old_option.subscription_option_id}.")

        # Create a new association
        new_association = ActiveSubscription(
            subscription_option_id=relevant_new_option.subscription_option_id,
            subscription_type=new_subscription_type,
            account_id=account.account_id,
            status=current_status) # Use status from the event

        db.add(new_association)
        # Commit immediately after this logical unit of work
        db.commit()
        logger.info(f"Successfully created new ActiveSubscription for account {account.account_id}, option {relevant_new_option.subscription_option_id}, status '{current_status}'")
    

def _update_subscription_state(
    db: Session,
    account: Account,
    price_id: str, # The *current* price ID from the event
    current_status: str,
    cancel_at_period_end: bool,
    canceled_at: Optional[int] # Timestamp
):
    """Updates the status and handles cancellation intent in the DB."""
    logger.info(f"Checking DB state for account {account.account_id}. Event state - Status: '{current_status}', Cancel@End: {cancel_at_period_end}")

    # Find the relevant ActiveSubscription record based on the *current* price ID
    # NOTE: Ideally, lookup by stripe_subscription_id if added to ActiveSubscription model
    active_sub = None
    if price_id:
        current_option = db.query(SubscriptionOption).filter(or_(
            SubscriptionOption.stripe_monthly_id == price_id,
            SubscriptionOption.stripe_yearly_id == price_id)).first()

        if current_option:
             active_sub = db.query(ActiveSubscription).filter_by(
                    subscription_option_id=current_option.subscription_option_id,
                    account_id=account.account_id).first()
        else:
            logger.warning(f"Could not find SubscriptionOption for current price_id {price_id} while checking state.")
            # Cannot proceed if we can't find the option associated with the current price
            return
    else:
        logger.warning("No current price_id found in event, cannot check subscription state accurately.")
        # Cannot proceed without a price ID to link to an option
        return


    if not active_sub:
        # Log if not found, but don't raise an error here as the price change logic might have just altered it,
        # or it might genuinely not exist (e.g., delayed webhook after deletion).
        logger.warning(f"No matching ActiveSubscription found for account {account.account_id} based on current price ID {price_id}. Cannot update state.")
        return

    logger.info(f"Found ActiveSubscription record {active_sub.active_subscription_id} with current DB status '{active_sub.status}'")

    # --- Update Status if Changed ---
    if active_sub.status != current_status:
        logger.warning(f"DB status ('{active_sub.status}') differs from event status ('{current_status}') for ActiveSubscription {active_sub.active_subscription_id}. Applying update.")
        # -----------------------------------------
        # <<<< PLACEHOLDER FOR STATUS UPDATE >>>>
        # TODO: Implement database update logic here.
        # Example:
        # active_sub.status = current_status
        # db.commit() # Commit specifically for status update
        # logger.info(f"Placeholder: Would update DB status to '{current_status}'")
        # TODO: Potentially send notifications/emails based on specific status transitions
        # -----------------------------------------
        pass # Remove pass when implementing
    else:
         logger.info("DB status matches event status. No status update needed.")

    # --- Handle Cancellation Intent ---
    # You might need a boolean field like `marked_for_cancellation` in your ActiveSubscription model
    if cancel_at_period_end:
        logger.warning(f"ActiveSubscription {active_sub.active_subscription_id} is marked to cancel at period end ({canceled_at}).")
         # ----------------------------------------------------
        # <<<< PLACEHOLDER FOR CANCELLATION INTENT >>>>
        # TODO: Implement logic for cancellation intent.
        # Example: Check if your DB flag needs setting
        # if not active_sub.marked_for_cancellation:
        #    active_sub.marked_for_cancellation = True
        #    # Optional: store the cancellation date too
        #    # active_sub.cancellation_effective_date = datetime.utcfromtimestamp(canceled_at) if canceled_at else None
        #    db.commit() # Commit specifically for cancellation flag update
        #    logger.info("Placeholder: Would mark subscription for cancellation in DB.")
        # ----------------------------------------------------
        pass # Remove pass when implementing
    # Optional: Handle if cancel_at_period_end becomes false (user un-cancels)
    # elif active_sub.marked_for_cancellation: # Check your flag
    #    logger.info(f"ActiveSubscription {active_sub.active_subscription_id} is no longer marked to cancel at period end.")
    #    # TODO: Unset your cancellation flag in the DB
    #    # active_sub.marked_for_cancellation = False
    #    # active_sub.cancellation_effective_date = None
    #    # db.commit()
    #    pass


# ==================================
# Main Event Handler Function
# ==================================

def handle_subscription_updated(event, db: Session):
    """Handles the customer.subscription.updated webhook event."""
    session = event["data"]["object"]

    # --- Extract Key Data ---
    customer_id = session.get("customer")
    subscription_id_stripe = session.get("id")
    current_status = session.get("status")
    cancel_at_period_end = session.get("cancel_at_period_end", False)
    canceled_at = session.get("canceled_at")
    new_price_id = session.get('items', {}).get('data', [{}])[0].get('price', {}).get('id')
    old_price_id_data = event.get('data', {}).get('previous_attributes', {}).get('items', {}).get('data', [{}])
    old_price_id = old_price_id_data[0].get('price', {}).get('id') if old_price_id_data else None

    logger.info(f"Handling subscription update event for Stripe Sub ID: {subscription_id_stripe}, Customer: {customer_id}")
    logger.debug(f"Full subscription updated event data: {json.dumps(event, indent=2)}")

    try:
        # --- Get Account ---
        account = _get_account_or_log_error(db, customer_id)
        if not account:
            return # Acknowledge webhook if account not found

        # --- Process Price Change (if applicable) ---
        is_price_change = bool(new_price_id != old_price_id and old_price_id and new_price_id)
        if is_price_change:
            _handle_price_change(db, account, old_price_id, new_price_id, current_status)
            # Optionally send email specific to price change here if needed
            # send_status_email("update", f"Subscription price updated. Old price: {old_price_id}, New price: {new_price_id}")
            # logger.info("Price update status email sent")
        else:
            logger.info("No price change detected in this event.")

        # --- Always Update Current Subscription State (Status, Cancellation) ---
        # This uses the *final* state after any potential price change.
        _update_subscription_state(
            db,
            account,
            new_price_id, # Pass the current price ID
            current_status,
            cancel_at_period_end,
            canceled_at
        )

    except Exception as e:
        db.rollback() # Rollback on any error during processing
        error_message = f"Error processing subscription update for Stripe Sub {subscription_id_stripe}: {str(e)}"
        logger.error(error_message)
        # Avoid logging raw event data here if already logged in debug, but log traceback
        logger.exception("Full traceback:")
        # Consider more specific error emails or alerts
        raise CustomException("Error!", error_message) # Re-raise standard exception or your custom one

    logger.info(f"Subscription update handling completed successfully for Stripe Sub ID: {subscription_id_stripe}")
@router.post(base_url + '/webhook', status_code=200)




async def webhook_received(request: Request, stripe_signature: str = Header(None), db: Session = Depends(get_db)):
    data = await request.body()
    webhook_secret = os.environ["STRIPE_WEBHOOK_SECRET"]

    logger.info("Received webhook from Stripe")

    try:
        try:
            event = stripe.Webhook.construct_event(
                payload=data,
                sig_header=stripe_signature,
                secret=webhook_secret
            )
            event_type = event['type']
            logger.info(f"Webhook event type: {event_type}")


        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Webhook signature verification failed: {e}")
            raise CustomException("Error!", f"⚠️  Webhook signature verification failed. {e}",
                                  status_code=500, log_level="WARNING", error_type="stripe_error")
        except Exception as e:
            logger.error(f"Error decoding Stripe event: {e}")
            raise CustomException("Error!", f"Something went wrong while decoding stripe event, error {e}",
                                  status_code=500, log_level="WARNING", error_type="stripe_error")

        if event_type == 'checkout.session.completed':
            logger.info("Handling checkout.session.completed event")
            handle_session_completed(event, db)
        elif event_type == 'customer.subscription.deleted':
            logger.info("Handling customer.subscription.deleted event")
            handle_subscription_deleted(event, db)
        elif event_type == 'radar.early_fraud_warning.created':
            logger.info("Handling radar.early_fraud_warning.created event")
            handle_early_fraud_warning(event, db)
        elif event_type == 'customer.subscription.updated':
            logger.info("Handling customer.subscription.updated event")
            handle_subscription_updated(event, db)
        else:
            logger.info(f"Unhandled event type: {event_type}")

        logger.info("Webhook processed successfully")
        return {"status": "success"}

    except Exception as e:
        logger.error(f"Error processing webhook: {e}")
        logger.exception("Full traceback:")
        raise CustomException("Error!", f"Something went wrong with stripe error {e}",
                              status_code=500, log_level="WARNING", error_type="stripe_error")
