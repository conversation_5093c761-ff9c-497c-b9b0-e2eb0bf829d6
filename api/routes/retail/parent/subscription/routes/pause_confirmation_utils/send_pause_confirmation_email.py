import boto3
from loguru import logger
import os
from .create_pause_confirmation_email import create_pause_confirmation_email

def send_pause_confirmation_email(email_address: str, language: str, resume_date: str, paused_count: int):
    """
    Send pause confirmation email to the user.
    
    Args:
        email_address: User's email address
        language: User's language preference ('de', 'en', 'fr', 'lu')
        resume_date: Formatted resume date string
        paused_count: Number of subscriptions paused
        
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    # Initialize SES client
    ses_client = boto3.client(
        'ses',
        region_name='eu-central-1',
        aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
        aws_secret_access_key=os.environ['AWS_SECRET_KEY']
    )

    # Create email content using templates
    email_body_html, email_body_text, email_subject = create_pause_confirmation_email(
        language, resume_date, paused_count
    )

    try:
        # Determine sender email based on environment
        sender_email = '<EMAIL>'
        if os.environ.get('ENVIRONMENT') == 'prod':
            sender_email = '<EMAIL>'
        elif os.environ.get('ENVIRONMENT') == 'test':
            sender_email = '<EMAIL>'

        # Send email to customer
        ses_client.send_email(
            Destination={'ToAddresses': [email_address]},
            Message={
                'Body': {
                    'Html': {'Charset': 'UTF-8', 'Data': email_body_html},
                    'Text': {'Charset': 'UTF-8', 'Data': email_body_text}
                },
                'Subject': {'Charset': 'UTF-8', 'Data': email_subject}
            },
            Source=sender_email
        )
        logger.info(f"Pause confirmation email sent to {email_address} (paused {paused_count} subscription(s))")
        
        # Send notification to support team
        try:
            support_subject = f"Subscription Pause Notification - {email_address}"
            support_body = f"""
Customer Email: {email_address}
Action: Subscription Paused
Subscription Count: {paused_count}
Resume Date: {resume_date}
Language: {language}

This is an automated notification that the customer has paused their subscription(s).
"""
            
            ses_client.send_email(
                Destination={'ToAddresses': ['<EMAIL>']},
                Message={
                    'Body': {
                        'Text': {'Charset': 'UTF-8', 'Data': support_body}
                    },
                    'Subject': {'Charset': 'UTF-8', 'Data': support_subject}
                },
                Source=sender_email
            )
            logger.info(f"Support team notification sent for pause by {email_address}")
        except Exception as support_email_error:
            logger.error(f"Failed to send support team notification for pause by {email_address}: {support_email_error}")
            # Don't fail if support email fails
        
        return True

    except Exception as e:
        logger.error(f"Error sending pause confirmation email to {email_address}: {e}")
        return False