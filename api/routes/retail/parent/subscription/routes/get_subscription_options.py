from fastapi import APIRouter, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account, Year, PriceVersion, PriceEligibility, SubscriptionOption, ActiveSubscription
from loguru import logger
import os
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.response import SubscriptionOptionsResponse

# This is your test secret API key.
router = APIRouter()
base_url = "/api/parent/subscription"
# AttributeError: 'ActiveSubscription' object has no attribute 'year_id'


@router.get(base_url + '/get-subscription-options', status_code=200, response_model=SubscriptionOptionsResponse)
def get_subscription_options(request: Request, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if parent_public_id:
        relevant_account = db.query(Account).filter_by(
            account_cognito_id=parent_public_id).first()
        if relevant_account:
            # First, determine which price version is applicable for this account
            account_price_eligibility = db.query(PriceEligibility).filter_by(
                account_id=relevant_account.account_id).first()

            if account_price_eligibility:
                # User has a specific price version assigned
                price_version = db.query(PriceVersion).filter_by(
                    price_version_id=account_price_eligibility.price_version_id).first()
            else:
                # Use the current default price version and create a price eligibility record
                price_version = db.query(PriceVersion).filter_by(
                    is_current=True, is_active=True).first()

                # Create a new price eligibility record
                new_price_eligibility = PriceEligibility(
                    account_id=relevant_account.account_id,
                    price_version_id=price_version.price_version_id
                )
                db.add(new_price_eligibility)
                db.commit()

            if not price_version:
                raise CustomException("No active price version found",
                                      status_code=500, error_type="configuration_error")

            # Fetch all subscriptions across years for the given price_version and join them with the years
            subscription_data = db.query(
                SubscriptionOption, Year
            ).join(
                Year, SubscriptionOption.year_id == Year.year_id
            ).filter(
                SubscriptionOption.price_version_id == price_version.price_version_id
            ).all()

            # Group data by year and system
            # Create a list instead of a dictionary to store multiple entries for the same year
            subscriptions_options = []
            for subscription, year in subscription_data:
                # Create an entry for classique system if applicable
                if year.is_classique:
                    subscriptions_options.append({
                        # This is used to match against active subs, will be removed later
                        "year_id": year.year_id,
                        "subscription_option_id": subscription.subscription_option_id,

                        # This will be part of the response
                        "year_name": year.year_name,
                        "year_public_id": year.year_public_id,
                        "system": "classique",
                        "subscription_details": {
                            "monthly_price": subscription.monthly_amount,
                            "yearly_price": subscription.yearly_amount
                        },
                        "active_subscription": False,
                        "active_subscription_type": None
                    })
                
                # Create an entry for general system if applicable
                if year.is_general:
                    subscriptions_options.append({
                        # This is used to match against active subs, will be removed later
                        "year_id": year.year_id,
                        "subscription_option_id": subscription.subscription_option_id,

                        # This will be part of the response
                        "year_name": year.year_name,
                        "year_public_id": year.year_public_id,
                        "system": "general",
                        "subscription_details": {
                            "monthly_price": subscription.monthly_amount,
                            "yearly_price": subscription.yearly_amount
                        },
                        "active_subscription": False,
                        "active_subscription_type": None
                    })

            # Fetch active subscriptions for the account and join with the subscription_option table to get the relevant public ids for the subscription options that the user is subscribed to
            relevant_active_subscriptions = (
                db.query(ActiveSubscription)
                .filter_by(account_id=relevant_account.account_id)
                .join(SubscriptionOption, ActiveSubscription.subscription_option_id == SubscriptionOption.subscription_option_id)
                .all()
            )

            for active_subscription in relevant_active_subscriptions:
                for year in subscriptions_options:
                    if year["subscription_option_id"] == active_subscription.subscription_option_id:
                        year["active_subscription"] = True
                        year["active_subscription_type"] = active_subscription.subscription_type

            # Need to remove the private year_id
            for option in subscriptions_options:
                option.pop("year_id")

            return SubscriptionOptionsResponse(subscription_options=subscriptions_options)
        else:
            raise CustomException("Not authorized!", "No corresponding Account found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No parent_account_public_id in token",
                              status_code=401, error_type="permission_error")
